{
  "version": "0.2.0",
  "configurations": [

    {
      // Use IntelliSense to find out which attributes exist for C# debugging
      // Use hover for the description of the existing attributes
      // For further information visit https://github.com/dotnet/vscode-csharp/blob/main/debugger-launchjson.md.
      "name": ".NET Core Launch (web)",
      "type": "coreclr",
      "request": "launch",
      "preLaunchTask": "build",
      // If you have changed target frameworks, make sure to update the program path.
      "program": "${workspaceFolder}/BldSrv.Web.Host/bin/Debug/net8.0/BldSrv.Web.Host.dll",
      "args": [],
      "cwd": "${workspaceFolder}/BldSrv.Web.Host",
      "stopAtEntry": false,
      // // Enable launching a web browser when ASP.NET Core starts. For more information: https://aka.ms/VSCode-CS-LaunchJson-WebBrowser
      // "serverReadyAction": {
      //     "action": "openExternally",
      //     "pattern": "\\bNow listening on:\\s+(https?://\\S+)"
      // },
      "env": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      },
      "sourceFileMap": {
        "/Views": "${workspaceFolder}/Views"
      }
    },
    {
      "name": ".NET Core Attach",
      "type": "coreclr",
      "request": "attach"
    },
    {
      "name": "Proptexx.Web.Auth",
      "type": "coreclr",
      "request": "launch",
      "preLaunchTask": "build",
      "program": "${workspaceFolder}/Proptexx.Web.Auth/bin/Debug/net9.0/Proptexx.Web.Auth.dll",
      "cwd": "${workspaceFolder}/Proptexx.Web.Auth",
      "env": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    },
    {
      "name": "Proptexx.Web.Api",
      "type": "coreclr",
      "request": "launch",
      "preLaunchTask": "build",
      "program": "${workspaceFolder}/Proptexx.Web.Api/bin/Debug/net9.0/Proptexx.Web.Api.dll",
      "cwd": "${workspaceFolder}/Proptexx.Web.Api",
      "env": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    },
    {
      "name": "Proptexx.Web.Command",
      "type": "coreclr",
      "request": "launch",
      "preLaunchTask": "build",
      "program": "${workspaceFolder}/Proptexx.Web.Command/bin/Debug/net9.0/Proptexx.Web.Command.dll",
      "cwd": "${workspaceFolder}/Proptexx.Web.Command",
      "env": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    },
    {
      "name": "Proptexx.Web.Query",
      "type": "coreclr",
      "request": "launch",
      "preLaunchTask": "build",
      "program": "${workspaceFolder}/Proptexx.Web.Query/bin/Debug/net9.0/Proptexx.Web.Query.dll",
      "cwd": "${workspaceFolder}/Proptexx.Web.Query",
      "env": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    },
    {
      "name": "Proptexx.Web.Partner",
      "type": "coreclr",
      "request": "launch",
      "preLaunchTask": "build",
      "program": "${workspaceFolder}/Proptexx.Web.Partner/bin/Debug/net9.0/Proptexx.Web.Partner.dll",
      "cwd": "${workspaceFolder}/Proptexx.Web.Partner",
      "env": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    },
    {
      "name": "Proptexx.Worker.Telemetry",
      "type": "coreclr",
      "request": "launch",
      "preLaunchTask": "build",
      "program": "${workspaceFolder}/Proptexx.Worker.Telemetry/bin/Debug/net9.0/Proptexx.Worker.Telemetry.dll",
      "cwd": "${workspaceFolder}/Proptexx.Worker.Telemetry",
      "env": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    },
    {
      "name": "Proptexx.Web.Webhook",
      "type": "coreclr",
      "request": "launch",
      "preLaunchTask": "build",
      "program": "${workspaceFolder}/Proptexx.Web.Webhook/bin/Debug/net9.0/Proptexx.Web.Webhook.dll",
      "cwd": "${workspaceFolder}/Proptexx.Web.Webhook",
      "env": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    },
    // {
    //   "name": "Proptexx.Worker.BatchWorker",
    //   "type": "coreclr",
    //   "request": "launch",
    //   "preLaunchTask": "build",
    //   "program": "${workspaceFolder}/Proptexx.Worker.BatchWorker/bin/Debug/net9.0/Proptexx.Worker.BatchWorker.dll",
    //   "cwd": "${workspaceFolder}/Proptexx.Worker.BatchWorker",
    //   "env": {
    //     "ASPNETCORE_ENVIRONMENT": "Development"
    //   }
    // },
    {
      "name": "Proptexx.Database.Migration",
      "type": "coreclr",
      "request": "launch",
      "preLaunchTask": "build",
      "program": "${workspaceFolder}/Proptexx.Database.Migration/bin/Debug/net9.0/Proptexx.Database.Migration.dll",
      "cwd": "${workspaceFolder}/Proptexx.Database.Migration",
      "env": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    }
  ],
  "compounds": [
    {
      "name": "Start All Proptexx Services",
      "configurations": [
        "Proptexx.Web.Auth",
        "Proptexx.Web.Api",
        "Proptexx.Web.Command",
        "Proptexx.Web.Query",
        "Proptexx.Web.Partner",
        "Proptexx.Worker.Telemetry",
        "Proptexx.Web.Webhook",
        // "Proptexx.Worker.BatchWorker"
        // "Proptexx.Worker.Telemetry"
        
      ]
    }
  ]
}
