using Dapper;
using Proptexx.Core.Cqrs.Query;
using Proptexx.Core.Postgresql;
using System;
using System.Threading.Tasks;

namespace Proptexx.Web.Query.Dashboard
{
    public sealed class GetDashboardSummary : BaseFilter, IQuery
    {
        // Private properties to store the calculated previous period dates.
        private DateTime PreviousStartDate { get; set; }
        private DateTime PreviousEndDate { get; set; }

        public async Task<object?> ExecuteAsync(QueryContext context)
        {
            // Open a PostgreSQL connection
            await using var npgsql = await context.OpenNpgsqlAsync(context.CancellationToken);
            PreviousStartDate = GetPreviousStartDate();
            PreviousEndDate = GetPreviousEndDate();

            // Telemetry Summary Query: Aggregates API, AI, Batch requests plus session data
            var telemetrySql = @"
                WITH telemetry_data AS (
                    SELECT 
                        CASE 
                            WHEN day BETWEEN @StartDate::date AND @EndDate::date THEN 'current'
                            WHEN day BETWEEN @PreviousStartDate::date AND @PreviousEndDate::date THEN 'previous'
                        END AS period,
                        total_api_requests,
                        total_ai_requests,
                        total_batch_requests,
                        average_request_duration,
                        distinct_sessions,
                        1 AS day_count
                    FROM telemetry.mv_dashboard_summary
                    WHERE day BETWEEN @PreviousStartDate::date AND @EndDate::date
                      AND (NULLIF(@WorkspaceId, ARRAY[]::UUID[]) IS NULL OR workspace_id = ANY(@WorkspaceId))
                )
                SELECT 
                    period,
                    SUM(total_api_requests) AS total_api_requests,
                    SUM(total_ai_requests) AS total_ai_requests,
                    SUM(total_batch_requests) AS total_batch_requests,
                    AVG(average_request_duration) AS average_request_duration,
                    SUM(distinct_sessions) AS total_sessions,
                    COUNT(*) AS days_count,
                    ROUND(SUM(total_api_requests)::NUMERIC / NULLIF(COUNT(*), 0), 1) AS average_requests_per_day,
                    ROUND(SUM(distinct_sessions)::NUMERIC / NULLIF(COUNT(*), 0), 1) AS avg_sessions_per_day
                FROM telemetry_data
                GROUP BY period;
            ";

            // Widget Metrics Summary Query: Aggregates widget sign-ins, renders, engagement time, etc.
            var widgetSql = @"
                WITH widget_data AS (
                    SELECT 
                        CASE 
                            WHEN day BETWEEN @StartDate::date AND @EndDate::date THEN 'current'
                            WHEN day BETWEEN @PreviousStartDate::date AND @PreviousEndDate::date THEN 'previous'
                        END AS period,
                        total_signins,
                        total_renders,
                        average_engagement_time,
                        average_renders_per_user,
                        top_room_type,
                        top_style
                    FROM telemetry.mv_widget_metrics_summary
                    WHERE day BETWEEN @PreviousStartDate::date AND @EndDate::date
                      AND (NULLIF(@WorkspaceId, ARRAY[]::UUID[]) IS NULL OR workspace_id = ANY(@WorkspaceId))
                )
                SELECT 
                    period,
                    SUM(total_signins) AS total_signins,
                    SUM(total_renders) AS total_renders,
                    AVG(average_engagement_time) AS average_engagement_time,
                    AVG(average_renders_per_user) AS average_renders_per_user,
                    MAX(top_room_type) AS top_room_type,
                    MAX(top_style) AS top_style
                FROM widget_data
                GROUP BY period;
            ";

            try
            {
                // Execute the telemetry query
                var telemetryResult = await npgsql.QueryAsync(telemetrySql, new
                {
                    StartDate,
                    EndDate,
                    PreviousStartDate,
                    PreviousEndDate,
                    WorkspaceId = Ids  // Assumes "Ids" is available from BaseFilter or similar
                });

                // Execute the widget metrics query
                var widgetResult = await npgsql.QueryAsync(widgetSql, new
                {
                    StartDate,
                    EndDate,
                    PreviousStartDate,
                    PreviousEndDate,
                    WorkspaceId = Ids
                });

                // Combine both query results into one object
                var result = new
                {
                    WorkspaceId = Ids,
                    Telemetry = telemetryResult,
                    Widgets = widgetResult
                };

                return result;
            }
            catch (Exception ex)
            {
                throw new Exception("An error occurred while executing the dashboard summary queries.", ex);
            }
        }

        // Calculate the previous period's start date based on the current period
        private DateTime GetPreviousStartDate()
        {
            if (!StartDate.HasValue || !EndDate.HasValue)
                throw new InvalidOperationException("StartDate and EndDate must have valid values.");
            return StartDate.Value.AddDays(-(EndDate.Value - StartDate.Value).Days - 1);
        }

        // Calculate the previous period's end date based on the current period
        private DateTime GetPreviousEndDate()
        {
            if (!StartDate.HasValue)
                throw new InvalidOperationException("StartDate must have a valid value.");
            return StartDate.Value.AddDays(-1);
        }
    }
}
